import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/router/app_routes.dart';
import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';

/// 主页面（包含底部导航）
class MainPage extends ConsumerStatefulWidget {
  const MainPage({super.key, required this.child});
  final Widget child;

  @override
  ConsumerState<MainPage> createState() => _MainPageState();
}

class _MainPageState extends ConsumerState<MainPage> {
  int _currentIndex = 0;

  /// 底部导航项配置
  final List<BottomNavigationBarItem> _bottomNavItems = [
    BottomNavigationBarItem(
      icon: Icon(Icons.home_outlined, size: 24.w),
      activeIcon: Icon(Icons.home, size: 24.w),
      label: '首页',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.shopping_cart_outlined, size: 24.w),
      activeIcon: Icon(Icons.shopping_cart, size: 24.w),
      label: '购物车',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.recycling_outlined, size: 24.w),
      activeIcon: Icon(Icons.recycling, size: 24.w),
      label: '回收',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.person_outline, size: 24.w),
      activeIcon: Icon(Icons.person, size: 24.w),
      label: '我的',
    ),
  ];

  /// 路由路径列表
  final List<String> _routes = [
    AppRoutes.home,
    AppRoutes.cart,
    AppRoutes.recycle,
    AppRoutes.profile,
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateCurrentIndex();
  }

  /// 更新当前选中的索引
  void _updateCurrentIndex() {
    final String location = GoRouterState.of(context).uri.toString();
    final index = _routes.indexOf(location);
    if (index != -1 && index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  /// 处理底部导航点击
  void _onBottomNavTap(int index) {
    if (index == _currentIndex) return;

    // 使用GoRouter进行导航
    context.go(_routes[index]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: _onBottomNavTap,
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: AppColors.primary,
            unselectedItemColor: AppColors.textTertiary,
            selectedLabelStyle: AppTextStyles.labelSmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: AppTextStyles.labelSmall,
            items: _bottomNavItems,
          ),
        ),
      ),
    );
  }
}
