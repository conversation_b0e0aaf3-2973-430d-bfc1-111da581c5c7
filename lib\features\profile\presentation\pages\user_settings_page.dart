import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/core/router/app_routes.dart';

/// 用户设置页面
class UserSettingsPage extends ConsumerWidget {
  const UserSettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: const CustomAppBar(
        title: '设置',
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 账户设置
            _buildAccountSection(context),
            
            SizedBox(height: 12.h),
            
            // 通用设置
            _buildGeneralSection(context),
            
            SizedBox(height: 12.h),
            
            // 隐私设置
            _buildPrivacySection(context),
            
            SizedBox(height: 12.h),
            
            // 其他设置
            _buildOtherSection(context),
            
            SizedBox(height: 32.h),
            
            // 退出登录按钮
            _buildLogoutButton(context),
            
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              '账户设置',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildSettingItem(
            context,
            icon: Icons.person_outline,
            title: '个人信息',
            subtitle: '编辑头像、昵称等',
            onTap: () => context.push(AppRoutes.userInfo),
          ),
          _buildSettingItem(
            context,
            icon: Icons.lock_outline,
            title: '修改密码',
            subtitle: '定期修改密码保护账户安全',
            onTap: () {
              // TODO: 跳转到修改密码页面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('修改密码功能开发中...')),
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.phone_outlined,
            title: '手机号码',
            subtitle: '用于登录和找回密码',
            onTap: () {
              // TODO: 跳转到修改手机号页面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('修改手机号功能开发中...')),
              );
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              '通用设置',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildSettingItem(
            context,
            icon: Icons.language_outlined,
            title: '语言设置',
            subtitle: '简体中文',
            onTap: () {
              // TODO: 语言设置
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('语言设置功能开发中...')),
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.dark_mode_outlined,
            title: '深色模式',
            subtitle: '跟随系统',
            onTap: () {
              // TODO: 主题设置
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('主题设置功能开发中...')),
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.notifications_outlined,
            title: '推送通知',
            subtitle: '管理消息推送设置',
            onTap: () => context.push(AppRoutes.notification),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              '隐私设置',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildSettingItem(
            context,
            icon: Icons.security_outlined,
            title: '隐私政策',
            subtitle: '了解我们如何保护您的隐私',
            onTap: () {
              // TODO: 隐私政策页面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('隐私政策页面开发中...')),
              );
            },
          ),
          _buildSettingItem(
            context,
            icon: Icons.description_outlined,
            title: '用户协议',
            subtitle: '查看用户服务协议',
            onTap: () {
              // TODO: 用户协议页面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('用户协议页面开发中...')),
              );
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildOtherSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              '其他',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          _buildSettingItem(
            context,
            icon: Icons.help_outline,
            title: '帮助与反馈',
            subtitle: '常见问题和意见反馈',
            onTap: () => context.push(AppRoutes.feedback),
          ),
          _buildSettingItem(
            context,
            icon: Icons.info_outline,
            title: '关于我们',
            subtitle: '版本信息和公司介绍',
            onTap: () => context.push(AppRoutes.about),
          ),
          _buildSettingItem(
            context,
            icon: Icons.cleaning_services_outlined,
            title: '清除缓存',
            subtitle: '清理应用缓存数据',
            onTap: () {
              _showClearCacheDialog(context);
            },
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: 24.w,
                  color: AppColors.textSecondary,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        subtitle,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: AppColors.textTertiary,
                ),
              ],
            ),
            if (showDivider) ...[
              SizedBox(height: 12.h),
              Divider(
                height: 1,
                color: AppColors.divider,
                indent: 36.w,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _showLogoutDialog(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red[50],
          foregroundColor: Colors.red[600],
          elevation: 0,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
            side: BorderSide(color: Colors.red[200]!),
          ),
        ),
        child: Text(
          '退出登录',
          style: AppTextStyles.bodyLarge.copyWith(
            color: Colors.red[600],
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现退出登录逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('退出登录功能开发中...')),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showClearCacheDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除缓存'),
        content: const Text('确定要清除应用缓存吗？这将删除临时文件和图片缓存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现清除缓存逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('缓存清除完成')),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
