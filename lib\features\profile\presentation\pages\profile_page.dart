import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/core/router/app_routes.dart';

/// 个人中心页面
class ProfilePage extends ConsumerWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: const CustomAppBar(title: '我的', showBackButton: false),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 用户信息区域
            _buildUserSection(context),

            // 功能菜单区域
            _buildMenuSection(context),

            // 设置区域
            _buildSettingsSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildUserSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(gradient: AppColors.primaryGradient),
      child: SafeArea(
        bottom: false,
        child: InkWell(
          onTap: () {
            // TODO: 跳转到登录页面或用户信息页面
            context.push(AppRoutes.login);
          },
          child: Row(
            children: [
              Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(30.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(Icons.person, size: 32.w, color: AppColors.primary),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '点击登录',
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '登录后享受更多服务',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, color: Colors.white, size: 16.w),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            context,
            icon: Icons.shopping_bag_outlined,
            title: '我的订单',
            onTap: () => context.push(AppRoutes.orderList),
          ),
          _buildMenuItem(
            context,
            icon: Icons.favorite_outline,
            title: '我的收藏',
            onTap: () {
              // TODO: 实现收藏页面路由
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('收藏功能开发中...')));
            },
          ),
          _buildMenuItem(
            context,
            icon: Icons.recycling_outlined,
            title: '我的回收',
            onTap: () => context.push(AppRoutes.recycleOrderList),
          ),
          _buildMenuItem(
            context,
            icon: Icons.location_on_outlined,
            title: '收货地址',
            onTap: () => context.push(AppRoutes.userAddress),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildMenuItem(
            context,
            icon: Icons.card_giftcard_outlined,
            title: '我的优惠券',
            onTap: () => context.push(AppRoutes.userCoupons),
          ),
          _buildMenuItem(
            context,
            icon: Icons.notifications_outlined,
            title: '消息通知',
            onTap: () => context.push(AppRoutes.notification),
          ),
          _buildMenuItem(
            context,
            icon: Icons.settings_outlined,
            title: '设置',
            onTap: () => context.push(AppRoutes.userSettings),
          ),
          _buildMenuItem(
            context,
            icon: Icons.help_outline,
            title: '帮助与反馈',
            onTap: () => context.push(AppRoutes.feedback),
          ),
          _buildMenuItem(
            context,
            icon: Icons.info_outline,
            title: '关于我们',
            onTap: () => context.push(AppRoutes.about),
            showDivider: false,
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Column(
          children: [
            Row(
              children: [
                Icon(icon, size: 24.w, color: AppColors.textSecondary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: AppColors.textTertiary,
                ),
              ],
            ),
            if (showDivider) ...[
              SizedBox(height: 16.h),
              Divider(height: 1, color: AppColors.divider, indent: 36.w),
            ],
          ],
        ),
      ),
    );
  }
}
