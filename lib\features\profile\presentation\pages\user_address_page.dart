import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/empty_widget.dart';
import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/core/router/app_routes.dart';

/// 地址管理页面
class UserAddressPage extends ConsumerStatefulWidget {
  const UserAddressPage({super.key});

  @override
  ConsumerState<UserAddressPage> createState() => _UserAddressPageState();
}

class _UserAddressPageState extends ConsumerState<UserAddressPage> {
  List<AddressModel> _addresses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  Future<void> _loadAddresses() async {
    setState(() => _isLoading = true);
    
    // TODO: 从API加载地址列表
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 模拟数据
    _addresses = [
      AddressModel(
        id: '1',
        name: '张三',
        phone: '13800138000',
        province: '广东省',
        city: '深圳市',
        district: '南山区',
        detail: '科技园南区深南大道10000号',
        isDefault: true,
      ),
      AddressModel(
        id: '2',
        name: '李四',
        phone: '13900139000',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        detail: '建国门外大街1号',
        isDefault: false,
      ),
    ];
    
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: '收货地址',
        showBackButton: true,
        actions: [
          TextButton(
            onPressed: () => _addAddress(),
            child: Text(
              '新增',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _addresses.isEmpty
              ? EmptyWidget(
                  icon: Icons.location_on_outlined,
                  title: '暂无收货地址',
                  subtitle: '添加收货地址，享受便捷购物体验',
                  buttonText: '添加地址',
                  onButtonPressed: _addAddress,
                )
              : RefreshIndicator(
                  onRefresh: _loadAddresses,
                  child: ListView.builder(
                    padding: EdgeInsets.all(16.w),
                    itemCount: _addresses.length,
                    itemBuilder: (context, index) {
                      final address = _addresses[index];
                      return _buildAddressCard(address, index);
                    },
                  ),
                ),
    );
  }

  Widget _buildAddressCard(AddressModel address, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: address.isDefault
            ? Border.all(color: AppColors.primary, width: 1)
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 地址信息
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 姓名和电话
                Row(
                  children: [
                    Text(
                      address.name,
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      address.phone,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    if (address.isDefault)
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 2.h,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          '默认',
                          style: AppTextStyles.labelSmall.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
                
                SizedBox(height: 8.h),
                
                // 地址详情
                Text(
                  '${address.province}${address.city}${address.district}${address.detail}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
          
          // 操作按钮
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.r),
                bottomRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                if (!address.isDefault)
                  TextButton(
                    onPressed: () => _setDefaultAddress(address.id),
                    child: Text(
                      '设为默认',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                const Spacer(),
                TextButton(
                  onPressed: () => _editAddress(address),
                  child: Text(
                    '编辑',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                TextButton(
                  onPressed: () => _deleteAddress(address.id, index),
                  child: Text(
                    '删除',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _addAddress() {
    context.push(AppRoutes.userAddressEdit);
  }

  void _editAddress(AddressModel address) {
    context.push('${AppRoutes.userAddressEdit}?id=${address.id}');
  }

  void _setDefaultAddress(String addressId) {
    setState(() {
      for (var address in _addresses) {
        address.isDefault = address.id == addressId;
      }
    });
    
    // TODO: 调用API设置默认地址
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已设为默认地址')),
    );
  }

  void _deleteAddress(String addressId, int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除地址'),
        content: const Text('确定要删除这个收货地址吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _addresses.removeAt(index);
              });
              
              // TODO: 调用API删除地址
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('地址已删除')),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

/// 地址模型
class AddressModel {
  final String id;
  final String name;
  final String phone;
  final String province;
  final String city;
  final String district;
  final String detail;
  bool isDefault;

  AddressModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.province,
    required this.city,
    required this.district,
    required this.detail,
    required this.isDefault,
  });
}
