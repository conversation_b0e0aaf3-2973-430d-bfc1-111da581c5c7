import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';

/// 地址编辑页面
class UserAddressEditPage extends ConsumerStatefulWidget {
  final String? addressId;
  
  const UserAddressEditPage({super.key, this.addressId});

  @override
  ConsumerState<UserAddressEditPage> createState() => _UserAddressEditPageState();
}

class _UserAddressEditPageState extends ConsumerState<UserAddressEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _provinceController = TextEditingController();
  final _cityController = TextEditingController();
  final _districtController = TextEditingController();
  final _detailController = TextEditingController();
  
  bool _isDefault = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.addressId != null) {
      _loadAddressData();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _provinceController.dispose();
    _cityController.dispose();
    _districtController.dispose();
    _detailController.dispose();
    super.dispose();
  }

  Future<void> _loadAddressData() async {
    setState(() => _isLoading = true);
    
    // TODO: 从API加载地址数据
    await Future.delayed(const Duration(milliseconds: 500));
    
    // 模拟数据
    _nameController.text = '张三';
    _phoneController.text = '13800138000';
    _provinceController.text = '广东省';
    _cityController.text = '深圳市';
    _districtController.text = '南山区';
    _detailController.text = '科技园南区深南大道10000号';
    _isDefault = true;
    
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.addressId != null;
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: isEdit ? '编辑地址' : '新增地址',
        showBackButton: true,
        actions: [
          TextButton(
            onPressed: _saveAddress,
            child: Text(
              '保存',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    SizedBox(height: 16.h),
                    
                    // 基本信息
                    _buildBasicInfoSection(),
                    
                    SizedBox(height: 16.h),
                    
                    // 地址信息
                    _buildAddressSection(),
                    
                    SizedBox(height: 16.h),
                    
                    // 设置选项
                    _buildSettingsSection(),
                    
                    SizedBox(height: 32.h),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '联系人信息',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          
          // 姓名
          _buildInputField(
            label: '收货人姓名',
            controller: _nameController,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入收货人姓名';
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // 手机号
          _buildInputField(
            label: '手机号码',
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入手机号码';
              }
              if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                return '请输入有效的手机号码';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAddressSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '地址信息',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          
          // 省市区选择
          Row(
            children: [
              Expanded(
                child: _buildSelectField(
                  label: '省份',
                  controller: _provinceController,
                  onTap: () => _selectProvince(),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildSelectField(
                  label: '城市',
                  controller: _cityController,
                  onTap: () => _selectCity(),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildSelectField(
                  label: '区县',
                  controller: _districtController,
                  onTap: () => _selectDistrict(),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // 详细地址
          _buildInputField(
            label: '详细地址',
            controller: _detailController,
            maxLines: 3,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入详细地址';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '设为默认地址',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
          Switch(
            value: _isDefault,
            onChanged: (value) {
              setState(() {
                _isDefault = value;
              });
            },
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          maxLines: maxLines,
          validator: validator,
          decoration: InputDecoration(
            hintText: '请输入$label',
            hintStyle: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.divider),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.divider),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 12.h,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectField({
    required String label,
    required TextEditingController controller,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        GestureDetector(
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.divider),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    controller.text.isEmpty ? '请选择$label' : controller.text,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: controller.text.isEmpty
                          ? AppColors.textTertiary
                          : AppColors.textPrimary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.textTertiary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _selectProvince() {
    // TODO: 实现省份选择
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('省份选择功能开发中...')),
    );
  }

  void _selectCity() {
    // TODO: 实现城市选择
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('城市选择功能开发中...')),
    );
  }

  void _selectDistrict() {
    // TODO: 实现区县选择
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('区县选择功能开发中...')),
    );
  }

  void _saveAddress() {
    if (_formKey.currentState!.validate()) {
      if (_provinceController.text.isEmpty ||
          _cityController.text.isEmpty ||
          _districtController.text.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请选择完整的省市区信息')),
        );
        return;
      }

      // TODO: 保存地址到后端
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('地址保存成功')),
      );
      Navigator.of(context).pop();
    }
  }
}
